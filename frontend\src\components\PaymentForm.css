.payment-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.payment-form__header {
  margin-bottom: 24px;
  text-align: center;
}

.payment-form__header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
}

.payment-form__amount {
  font-size: 18px;
  font-weight: 500;
  color: #2d3748;
  background: #f7fafc;
  padding: 8px 16px;
  border-radius: 6px;
  display: inline-block;
}

.payment-form__card {
  margin-bottom: 20px;
}

.payment-form__label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.payment-form__card-element {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  background: #ffffff;
  transition: border-color 0.2s ease;
}

.payment-form__card-element:focus-within {
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.payment-form__save-card {
  margin-bottom: 20px;
}

.payment-form__checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #4a5568;
}

.payment-form__checkbox input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #3182ce;
}

.payment-form__checkbox span {
  user-select: none;
}

.payment-form__error {
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

.payment-form__error--card {
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.payment-form__error--general {
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.payment-form__actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.payment-form__button {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 120px;
}

.payment-form__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.payment-form__button--cancel {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.payment-form__button--cancel:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.payment-form__button--submit {
  background: #3182ce;
  color: #ffffff;
}

.payment-form__button--submit:hover:not(:disabled) {
  background: #2c5aa0;
}

.payment-form__button--submit:disabled {
  background: #a0aec0;
}

.payment-form__status {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.payment-form__status small {
  color: #718096;
  font-size: 12px;
}

/* Loading animation */
.payment-form__button--submit:disabled::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 640px) {
  .payment-form {
    margin: 16px;
    padding: 20px;
  }

  .payment-form__actions {
    flex-direction: column;
  }

  .payment-form__button {
    width: 100%;
  }
}

/* Success state */
.payment-form--success {
  border: 2px solid #48bb78;
}

.payment-form--success .payment-form__header {
  color: #2f855a;
}

/* Error state */
.payment-form--error {
  border: 2px solid #f56565;
}

/* Focus styles for accessibility */
.payment-form__button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.5);
}

.payment-form__checkbox input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.5);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .payment-form {
    background: #2d3748;
    color: #e2e8f0;
  }

  .payment-form__header h3 {
    color: #f7fafc;
  }

  .payment-form__amount {
    background: #4a5568;
    color: #e2e8f0;
  }

  .payment-form__label {
    color: #e2e8f0;
  }

  .payment-form__card-element {
    background: #4a5568;
    border-color: #718096;
  }

  .payment-form__button--cancel {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
  }

  .payment-form__button--cancel:hover:not(:disabled) {
    background: #2d3748;
  }
}
