{"name": "realtime-order-management-system", "version": "1.0.0", "description": "A microservice with event-driven architecture using Node.js and React", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && npm run dev", "frontend:dev": "cd frontend && npm run dev", "backend:start": "cd backend && npm start", "frontend:start": "cd frontend && npm start", "backend:test": "cd backend && npm test", "frontend:test": "cd frontend && npm test", "test": "node test-runner.js", "test:backend": "node test-runner.js --frontend-only", "test:frontend": "node test-runner.js --backend-only", "test:unit": "npm run backend:test:unit && npm run frontend:test:unit", "test:integration": "npm run backend:test:integration && npm run frontend:test:integration", "test:e2e": "npm run frontend:test:e2e", "test:coverage": "npm run backend:test:coverage && npm run frontend:test:coverage", "test:watch": "concurrently \"npm run backend:test:watch\" \"npm run frontend:test:watch\"", "backend:test:unit": "cd backend && npm run test:unit", "backend:test:integration": "cd backend && npm run test:integration", "backend:test:coverage": "cd backend && npm run test:coverage", "backend:test:watch": "cd backend && npm run test:watch", "frontend:test:unit": "cd frontend && npm run test:unit", "frontend:test:components": "cd frontend && npm run test:components", "frontend:test:integration": "cd frontend && npm run test:integration", "frontend:test:e2e": "cd frontend && npm run test:e2e", "frontend:test:coverage": "cd frontend && npm run test:coverage", "frontend:test:watch": "cd frontend && npm run test:watch", "lint": "npm run backend:lint && npm run frontend:lint", "backend:lint": "cd backend && npm run lint", "frontend:lint": "cd frontend && npm run lint", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "npm run backend:clean && npm run frontend:clean", "backend:clean": "cd backend && rm -rf node_modules coverage", "frontend:clean": "cd frontend && rm -rf node_modules coverage dist"}, "keywords": ["microservice", "event-driven", "nodejs", "react", "architecture"], "author": "Developer", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}